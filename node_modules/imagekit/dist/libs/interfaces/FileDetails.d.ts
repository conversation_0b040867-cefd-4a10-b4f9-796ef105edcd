import { FileType } from "./FileType";
export interface EmbeddedMetadataValues {
    [key: string]: string | number | boolean | Date | Array<string | number | boolean | Date>;
}
export interface AITagItem {
    name: string;
    confidence: number;
    source: 'google-auto-tagging' | 'aws-auto-tagging';
}
export interface CMValues {
    [key: string]: string | number | boolean | Array<string | number | boolean>;
}
interface BgRemoval {
    name: string;
    options: {
        bg_color?: string;
        bg_image_url?: string;
        add_shadow: boolean;
        semitransparency: boolean;
    };
}
interface AutoTag {
    name: string;
    maxTags: number;
    minConfidence: number;
}
export declare type Extension = (BgRemoval | AutoTag)[];
/**
 * Options when updating file details such as tags and customCoordinates attribute using update file detail API.
 *
 * @see {@link https://docs.imagekit.io/api-reference/media-api/update-file-details}
 */
export interface FileDetailsOptions {
    /**
     * Array of tags associated with the file.
     */
    tags?: string[];
    /**
     * Define an important area in the image.
     * Example - 50,50,500,500
     */
    customCoordinates?: string;
    extensions?: Extension;
    webhookUrl?: string;
    removeAITags?: string[];
    customMetadata?: CMValues;
    /**
     * Configure the publication status of a file and its versions.
     */
    publish?: {
        isPublished: boolean;
        includeFileVersions?: boolean;
    };
}
/**
 *
 * File object.
 *
 * @see {@link https://docs.imagekit.io/api-reference/media-api#file-object-structure}
 */
export interface FileObject {
    /**
     * The unique fileId of the uploaded file.
     */
    fileId: string;
    /**
     * Type of item. It can be either file, file-version or folder.
     */
    type: "file" | "file-version";
    /**
     * Name of the file or folder.
     */
    name: string;
    /**
     * The relative path of the file. In case of image, you can use this
     * path to construct different transformations.
     */
    filePath: string;
    /**
     * Array of tags associated with the image. If no tags are set, it will be null.
     */
    tags?: string[] | null;
    /**
     * Is the file marked as private. It can be either true or false.
     */
    isPrivateFile: boolean;
    /**
     * Value of custom coordinates associated with the image in format x,y,width,height.
     * If customCoordinates are not defined then it is null.
     */
    customCoordinates: string | null;
    /**
     * A publicly accessible URL of the file.
     */
    url: string;
    /**
     * In case of an image, a small thumbnail URL.
     */
    thumbnail: string;
    /**
     * The type of file, it could be either image or non-image.
     */
    fileType: FileType;
    AITags?: AITagItem[];
    extensionStatus?: {
        [key: string]: string;
    };
    embeddedMetadata?: EmbeddedMetadataValues | null;
    customMetadata?: CMValues;
    size: number;
    createdAt: string;
    updatedAt: string;
    height: number;
    width: number;
    hasAlpha: boolean;
    mime?: string;
    /**
     * An object containing the file or file version's id (versionId) and name.
     */
    versionInfo?: {
        name: string;
        id: string;
    };
}
/**
 *
 * Folder object.
 *
 * @see {@link https://docs.imagekit.io/api-reference/media-api#file-object-structure}
 */
export interface FolderObject {
    /**
     * The unique fileId of the folder.
     */
    folderId: string;
    /**
     * Type of item. It can be either file, file-version or folder.
     */
    type: "folder";
    /**
     * Name of the file or folder.
     */
    name: string;
    /**
     * The relative path of the folder.
     */
    folderPath: string;
    createdAt: string;
    updatedAt: string;
}
export interface FileVersionDetailsOptions {
    /**
     * The unique fileId of the uploaded file. fileId is returned in list files API and upload API.
     */
    fileId: string;
    /**
     * The unique versionId of the uploaded file's version. This is returned in list files API and upload API as id within the versionInfo parameter.
     */
    versionId: string;
}
export {};
