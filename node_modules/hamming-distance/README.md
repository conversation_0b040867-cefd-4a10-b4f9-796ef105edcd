
# hamming-distance

[![NPM version][npm-image]][npm-url]
[![Build status][travis-image]][travis-url]
[![Test coverage][coveralls-image]][coveralls-url]
[![Dependency Status][david-image]][david-url]
[![License][license-image]][license-url]
[![Downloads][downloads-image]][downloads-url]
[![Gittip][gittip-image]][gittip-url]

## Usage

```js
var compare = require('hamming-distance');

var distance = compare(new Buffer('0000', 'hex'), new Buffer('000F', 'hex'));
```

[gitter-image]: https://badges.gitter.im/math-utils/hamming-distance.png
[gitter-url]: https://gitter.im/math-utils/hamming-distance
[npm-image]: https://img.shields.io/npm/v/hamming-distance.svg?style=flat-square
[npm-url]: https://npmjs.org/package/hamming-distance
[github-tag]: http://img.shields.io/github/tag/math-utils/hamming-distance.svg?style=flat-square
[github-url]: https://github.com/math-utils/hamming-distance/tags
[travis-image]: https://img.shields.io/travis/math-utils/hamming-distance.svg?style=flat-square
[travis-url]: https://travis-ci.org/math-utils/hamming-distance
[coveralls-image]: https://img.shields.io/coveralls/math-utils/hamming-distance.svg?style=flat-square
[coveralls-url]: https://coveralls.io/r/math-utils/hamming-distance
[david-image]: http://img.shields.io/david/math-utils/hamming-distance.svg?style=flat-square
[david-url]: https://david-dm.org/math-utils/hamming-distance
[license-image]: http://img.shields.io/npm/l/hamming-distance.svg?style=flat-square
[license-url]: LICENSE
[downloads-image]: http://img.shields.io/npm/dm/hamming-distance.svg?style=flat-square
[downloads-url]: https://npmjs.org/package/hamming-distance
[gittip-image]: https://img.shields.io/gratipay/jonathanong.svg?style=flat-square
[gittip-url]: https://gratipay.com/jonathanong/
