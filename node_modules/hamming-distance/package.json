{"name": "hamming-distance", "description": "Calculate the hamming distance of two hex strings or buffers", "version": "1.0.0", "author": "<PERSON> <<EMAIL>> (http://jongleberry.com)", "license": "MIT", "repository": "math-utils/hamming-distance", "dependencies": {}, "devDependencies": {"mocha": "2", "istanbul": "0"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "keywords": ["hamming", "distance"], "files": "index.js"}