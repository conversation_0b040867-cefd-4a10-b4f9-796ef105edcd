declare const _default: {
    listFiles: (listOptions: import("../interfaces").ListFileOptions, defaultOptions: import("../interfaces").ImageKitOptions, callback?: import("../interfaces").IKCallback<import("../interfaces").FileObject[], Error> | undefined) => void;
    getFileDetails: (fileId: string, defaultOptions: import("../interfaces").ImageKitOptions, callback?: import("../interfaces").IKCallback<import("../interfaces").FileObject, Error> | undefined) => void;
    getFileVersions: (fileId: string, defaultOptions: import("../interfaces").ImageKitOptions, callback?: import("../interfaces").IKCallback<import("../interfaces").FileObject[], Error> | undefined) => void;
    getFileVersionDetails: (fileDetailsOptions: import("../interfaces").FileVersionDetailsOptions, defaultOptions: import("../interfaces").ImageKitOptions, callback?: import("../interfaces").IKCallback<import("../interfaces").FileObject, Error> | undefined) => void;
    updateFileDetails: (fileId: string, updateData: import("../interfaces").FileDetailsOptions, defaultOptions: import("../interfaces").ImageKitOptions, callback?: import("../interfaces").IKCallback<import("../interfaces").FileObject, Error> | undefined) => void;
    getFileMetadata: (fileIdOrURL: string, defaultOptions: import("../interfaces").ImageKitOptions, callback?: import("../interfaces").IKCallback<import("../interfaces").FileMetadataResponse, Error> | undefined) => void;
    deleteFile: (fileId: string, defaultOptions: import("../interfaces").ImageKitOptions, callback?: import("../interfaces").IKCallback<void, Error> | undefined) => void;
    bulkDeleteFiles: (fileIdArray: string[], defaultOptions: import("../interfaces").ImageKitOptions, callback?: import("../interfaces").IKCallback<import("../interfaces").BulkDeleteFilesResponse, import("../interfaces").BulkDeleteFilesError> | undefined) => void;
    deleteFileVersion: (deleteFileVersionOptions: import("../interfaces").DeleteFileVersionOptions, defaultOptions: import("../interfaces").ImageKitOptions, callback?: import("../interfaces").IKCallback<void, Error> | undefined) => void;
    restoreFileVersion: (restoreFileVersionOptions: import("../interfaces").RestoreFileVersionOptions, defaultOptions: import("../interfaces").ImageKitOptions, callback?: import("../interfaces").IKCallback<import("../interfaces").FileObject, Error> | undefined) => void;
    bulkAddTags: (fileIdArray: string[], tags: string[], defaultOptions: import("../interfaces").ImageKitOptions, callback?: import("../interfaces").IKCallback<void, Error> | undefined) => void;
    bulkRemoveTags: (fileIdArray: string[], tags: string[], defaultOptions: import("../interfaces").ImageKitOptions, callback?: import("../interfaces").IKCallback<void, Error> | undefined) => void;
    bulkRemoveAITags: (fileIdArray: string[], tags: string[], defaultOptions: import("../interfaces").ImageKitOptions, callback?: import("../interfaces").IKCallback<void, Error> | undefined) => void;
    copyFile: (copyFileOptions: import("../interfaces").CopyFileOptions, defaultOptions: import("../interfaces").ImageKitOptions, callback?: import("../interfaces").IKCallback<void, Error> | undefined) => void;
    moveFile: (moveFileOptions: import("../interfaces").MoveFileOptions, defaultOptions: import("../interfaces").ImageKitOptions, callback?: import("../interfaces").IKCallback<void, Error> | undefined) => void;
    renameFile: (renameFileOptions: import("../interfaces").RenameFileOptions, defaultOptions: import("../interfaces").ImageKitOptions, callback?: import("../interfaces").IKCallback<import("../interfaces").RenameFileResponse, Error> | undefined) => void;
    copyFolder: (copyFolderOptions: import("../interfaces").CopyFolderOptions, defaultOptions: import("../interfaces").ImageKitOptions, callback?: import("../interfaces").IKCallback<import("../interfaces").CopyFolderResponse, Error> | undefined) => void;
    moveFolder: (moveFolderOptions: import("../interfaces").MoveFolderOptions, defaultOptions: import("../interfaces").ImageKitOptions, callback?: import("../interfaces").IKCallback<void, Error> | undefined) => void;
    createFolder: (createFolderOptions: import("../interfaces").CreateFolderOptions, defaultOptions: import("../interfaces").ImageKitOptions, callback?: import("../interfaces").IKCallback<void, Error> | undefined) => void;
    deleteFolder: (folderPath: string, defaultOptions: import("../interfaces").ImageKitOptions, callback?: import("../interfaces").IKCallback<void, Error> | undefined) => void;
    getBulkJobStatus: (jobId: string, defaultOptions: import("../interfaces").ImageKitOptions, callback?: import("../interfaces").IKCallback<void, Error> | undefined) => void;
    purgeCache: (url: string, defaultOptions: import("../interfaces").ImageKitOptions, callback?: import("../interfaces").IKCallback<import("../interfaces").PurgeCacheResponse, Error> | undefined) => void;
    getPurgeCacheStatus: (requestId: string, defaultOptions: import("../interfaces").ImageKitOptions, callback?: import("../interfaces").IKCallback<import("../interfaces").PurgeCacheStatusResponse, Error> | undefined) => void;
    createCustomMetadataField: (createCustomMetadataFieldOptions: import("../interfaces").CreateCustomMetadataFieldOptions, defaultOptions: import("../interfaces").ImageKitOptions, callback?: import("../interfaces").IKCallback<import("../interfaces").CustomMetadataField, Error> | undefined) => void;
    getCustomMetadataFields: (getCustomMetadataFieldsOptions: import("../interfaces").GetCustomMetadataFieldsOptions, defaultOptions: import("../interfaces").ImageKitOptions, callback?: import("../interfaces").IKCallback<import("../interfaces").CustomMetadataField[], Error> | undefined) => void;
    updateCustomMetadataField: (fieldId: string, updateCustomMetadataFieldOptions: import("../interfaces").UpdateCustomMetadataFieldOptions, defaultOptions: import("../interfaces").ImageKitOptions, callback?: import("../interfaces").IKCallback<import("../interfaces").CustomMetadataField, Error> | undefined) => void;
    deleteCustomMetadataField: (fieldId: string, defaultOptions: import("../interfaces").ImageKitOptions, callback?: import("../interfaces").IKCallback<void, Error> | undefined) => void;
};
export default _default;
