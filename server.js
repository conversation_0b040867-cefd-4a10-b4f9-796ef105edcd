var ImageKit = require("imagekit");
var fs = require('fs');
var https = require('https');

var imagekit = new ImageKit({
    publicKey : "public_Q8avZAesIfF2SEOFiBwIqukGGXk=",
    privateKey : "private_ko9POvLTH2+l1VTJlUr+bFwB6AE=",
    urlEndpoint : "https://ik.imagekit.io/aayang"
});

var authenticationParameters = imagekit.getAuthenticationParameters();
console.log(authenticationParameters);

// Function to get account usage/limits
function getAccountUsage(startDate, endDate) {
    return new Promise((resolve, reject) => {
        // Construct query parameters
        const queryParams = new URLSearchParams();
        if (startDate) queryParams.append('startDate', startDate);
        if (endDate) queryParams.append('endDate', endDate);

        const path = `/v1/accounts/usage${queryParams.toString() ? '?' + queryParams.toString() : ''}`;

        // Create basic auth header
        const auth = Buffer.from(imagekit.options.privateKey + ':').toString('base64');

        const options = {
            hostname: 'api.imagekit.io',
            port: 443,
            path: path,
            method: 'GET',
            headers: {
                'Authorization': `Basic ${auth}`,
                'Content-Type': 'application/json'
            }
        };

        const req = https.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    if (res.statusCode === 200) {
                        resolve(response);
                    } else {
                        reject(new Error(`API Error: ${res.statusCode} - ${response.message || data}`));
                    }
                } catch (error) {
                    reject(new Error(`Failed to parse response: ${error.message}`));
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        req.end();
    });
}

// Example usage - get account usage for different time periods
async function checkAccountLimits() {
    try {
        // Get current date and first day of current month
        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

        const startDate = startOfMonth.toISOString().split('T')[0]; // Format: YYYY-MM-DD
        const endDate = now.toISOString().split('T')[0];

        console.log(`Getting usage from ${startDate} to ${endDate}...`);

        const usage = await getAccountUsage(startDate, endDate);
        console.log('Current Month Usage:', JSON.stringify(usage, null, 2));

        // Get usage for the last 30 days
        const thirtyDaysAgo = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));
        const thirtyDaysAgoStr = thirtyDaysAgo.toISOString().split('T')[0];

        console.log(`\nGetting usage for last 30 days (${thirtyDaysAgoStr} to ${endDate})...`);
        const last30DaysUsage = await getAccountUsage(thirtyDaysAgoStr, endDate);
        console.log('Last 30 Days Usage:', JSON.stringify(last30DaysUsage, null, 2));

        // Get enhanced account limits with remaining quota
        console.log('\n=== GETTING ENHANCED ACCOUNT LIMITS ===');
        const limits = await getAccountLimits();

        // Display usage in a more readable format
        console.log('\n=== USAGE SUMMARY WITH REMAINING QUOTA ===');
        console.log(`📊 Current Usage:`);
        console.log(`  Bandwidth Used: ${limits.usageFormatted.bandwidthGB} GB`);
        console.log(`  Storage Used: ${limits.usageFormatted.storageMB} MB`);
        console.log(`  Video Processing Units: ${limits.usageFormatted.videoProcessingUnits}`);
        console.log(`  Extension Units: ${limits.usageFormatted.extensionUnits}`);

        console.log(`\n📈 Estimated Plan Limits (${limits.estimatedLimits.estimatedPlan}):`);
        console.log(`  Bandwidth Limit: ${limits.estimatedLimits.bandwidthGB} GB`);
        console.log(`  Storage Limit: ${limits.estimatedLimits.storageGB} GB`);

        console.log(`\n✅ Remaining Quota:`);
        console.log(`  Bandwidth Remaining: ${limits.remaining.bandwidthGB} GB`);
        console.log(`  Storage Remaining: ${limits.remaining.storageGB} GB`);

        console.log(`\n⚠️  ${limits.note}`);

    } catch (error) {
        console.error('Error getting account usage:', error.message);
    }
}

// Function to try different potential endpoints for account/plan information
async function tryGetAccountInfo() {
    const auth = Buffer.from(imagekit.options.privateKey + ':').toString('base64');
    const potentialEndpoints = [
        '/v1/accounts',
        '/v1/accounts/info',
        '/v1/accounts/plan',
        '/v1/accounts/subscription',
        '/v1/accounts/limits',
        '/v1/accounts/quota',
        '/v1/plan',
        '/v1/subscription',
        '/v1/limits'
    ];

    for (const endpoint of potentialEndpoints) {
        try {
            console.log(`Trying endpoint: ${endpoint}`);
            const result = await makeAPIRequest(endpoint, auth);
            console.log(`✅ Success on ${endpoint}:`, JSON.stringify(result, null, 2));
            return result;
        } catch (error) {
            console.log(`❌ Failed on ${endpoint}: ${error.message}`);
        }
    }

    return null;
}

// Generic API request function
function makeAPIRequest(path, auth) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'api.imagekit.io',
            port: 443,
            path: path,
            method: 'GET',
            headers: {
                'Authorization': `Basic ${auth}`,
                'Content-Type': 'application/json'
            }
        };

        const req = https.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    if (res.statusCode === 200) {
                        resolve(response);
                    } else {
                        reject(new Error(`${res.statusCode} - ${response.message || data}`));
                    }
                } catch (error) {
                    reject(new Error(`Failed to parse response: ${error.message}`));
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        req.end();
    });
}

// Helper function to get account limits/plan information with estimated limits
async function getAccountLimits() {
    try {
        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const startDate = startOfMonth.toISOString().split('T')[0];
        const endDate = now.toISOString().split('T')[0];

        const usage = await getAccountUsage(startDate, endDate);

        // Try to get additional account info
        console.log('\n=== Trying to find account plan information ===');
        const accountInfo = await tryGetAccountInfo();

        // Common ImageKit plan limits (you should update these based on your actual plan)
        const commonPlanLimits = {
            free: { bandwidth: 20 * 1024 * 1024 * 1024, storage: 20 * 1024 * 1024 * 1024 }, // 20GB each
            starter: { bandwidth: 40 * 1024 * 1024 * 1024, storage: 40 * 1024 * 1024 * 1024 }, // 40GB each
            basic: { bandwidth: 100 * 1024 * 1024 * 1024, storage: 100 * 1024 * 1024 * 1024 }, // 100GB each
            standard: { bandwidth: 250 * 1024 * 1024 * 1024, storage: 250 * 1024 * 1024 * 1024 }, // 250GB each
            pro: { bandwidth: 500 * 1024 * 1024 * 1024, storage: 500 * 1024 * 1024 * 1024 } // 500GB each
        };

        // Estimate plan based on usage patterns (this is a rough estimate)
        let estimatedPlan = 'unknown';
        const monthlyBandwidth = usage.bandwidthBytes;

        if (monthlyBandwidth < commonPlanLimits.free.bandwidth) estimatedPlan = 'free';
        else if (monthlyBandwidth < commonPlanLimits.starter.bandwidth) estimatedPlan = 'starter';
        else if (monthlyBandwidth < commonPlanLimits.basic.bandwidth) estimatedPlan = 'basic';
        else if (monthlyBandwidth < commonPlanLimits.standard.bandwidth) estimatedPlan = 'standard';
        else estimatedPlan = 'pro or higher';

        const estimatedLimits = commonPlanLimits[estimatedPlan] || commonPlanLimits.free;

        return {
            currentUsage: usage,
            usageFormatted: {
                bandwidthGB: (usage.bandwidthBytes / (1024 * 1024 * 1024)).toFixed(2),
                storageMB: (usage.mediaLibraryStorageBytes / (1024 * 1024)).toFixed(2),
                videoProcessingUnits: usage.videoProcessingUnitsCount,
                extensionUnits: usage.extensionUnitsCount,
                originalCacheStorageMB: (usage.originalCacheStorageBytes / (1024 * 1024)).toFixed(2)
            },
            estimatedLimits: {
                bandwidthGB: (estimatedLimits.bandwidth / (1024 * 1024 * 1024)).toFixed(0),
                storageGB: (estimatedLimits.storage / (1024 * 1024 * 1024)).toFixed(0),
                estimatedPlan: estimatedPlan
            },
            remaining: {
                bandwidthGB: ((estimatedLimits.bandwidth - usage.bandwidthBytes) / (1024 * 1024 * 1024)).toFixed(2),
                storageGB: ((estimatedLimits.storage - usage.mediaLibraryStorageBytes) / (1024 * 1024 * 1024)).toFixed(2)
            },
            period: {
                startDate,
                endDate
            },
            accountInfo: accountInfo,
            note: "Limits are estimated based on common ImageKit plans. Check your dashboard for exact limits."
        };
    } catch (error) {
        throw new Error(`Failed to get account limits: ${error.message}`);
    }
}

// Function to get account limits with custom plan limits
async function getAccountLimitsWithCustomPlan(customLimits = null) {
    try {
        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const startDate = startOfMonth.toISOString().split('T')[0];
        const endDate = now.toISOString().split('T')[0];

        const usage = await getAccountUsage(startDate, endDate);

        // Use custom limits if provided, otherwise use estimated limits
        let planLimits;
        let planName;

        if (customLimits) {
            planLimits = {
                bandwidth: customLimits.bandwidthGB * 1024 * 1024 * 1024,
                storage: customLimits.storageGB * 1024 * 1024 * 1024
            };
            planName = customLimits.planName || 'custom';
        } else {
            // Use the estimation logic from getAccountLimits
            const commonPlanLimits = {
                free: { bandwidth: 20 * 1024 * 1024 * 1024, storage: 20 * 1024 * 1024 * 1024 },
                starter: { bandwidth: 40 * 1024 * 1024 * 1024, storage: 40 * 1024 * 1024 * 1024 },
                basic: { bandwidth: 100 * 1024 * 1024 * 1024, storage: 100 * 1024 * 1024 * 1024 },
                standard: { bandwidth: 250 * 1024 * 1024 * 1024, storage: 250 * 1024 * 1024 * 1024 },
                pro: { bandwidth: 500 * 1024 * 1024 * 1024, storage: 500 * 1024 * 1024 * 1024 }
            };

            const monthlyBandwidth = usage.bandwidthBytes;
            if (monthlyBandwidth < commonPlanLimits.free.bandwidth) planName = 'free';
            else if (monthlyBandwidth < commonPlanLimits.starter.bandwidth) planName = 'starter';
            else if (monthlyBandwidth < commonPlanLimits.basic.bandwidth) planName = 'basic';
            else if (monthlyBandwidth < commonPlanLimits.standard.bandwidth) planName = 'standard';
            else planName = 'pro or higher';

            planLimits = commonPlanLimits[planName] || commonPlanLimits.free;
        }

        const bandwidthUsagePercent = ((usage.bandwidthBytes / planLimits.bandwidth) * 100).toFixed(1);
        const storageUsagePercent = ((usage.mediaLibraryStorageBytes / planLimits.storage) * 100).toFixed(1);

        return {
            currentUsage: usage,
            usageFormatted: {
                bandwidthGB: (usage.bandwidthBytes / (1024 * 1024 * 1024)).toFixed(2),
                storageMB: (usage.mediaLibraryStorageBytes / (1024 * 1024)).toFixed(2),
                videoProcessingUnits: usage.videoProcessingUnitsCount,
                extensionUnits: usage.extensionUnitsCount,
                originalCacheStorageMB: (usage.originalCacheStorageBytes / (1024 * 1024)).toFixed(2)
            },
            planLimits: {
                bandwidthGB: (planLimits.bandwidth / (1024 * 1024 * 1024)).toFixed(0),
                storageGB: (planLimits.storage / (1024 * 1024 * 1024)).toFixed(0),
                planName: planName
            },
            remaining: {
                bandwidthGB: ((planLimits.bandwidth - usage.bandwidthBytes) / (1024 * 1024 * 1024)).toFixed(2),
                storageGB: ((planLimits.storage - usage.mediaLibraryStorageBytes) / (1024 * 1024 * 1024)).toFixed(2)
            },
            usagePercentage: {
                bandwidth: bandwidthUsagePercent,
                storage: storageUsagePercent
            },
            period: {
                startDate,
                endDate
            },
            isCustomPlan: !!customLimits
        };
    } catch (error) {
        throw new Error(`Failed to get account limits: ${error.message}`);
    }
}

// Export the functions for use in other modules
module.exports = {
    imagekit,
    getAccountUsage,
    getAccountLimits,
    getAccountLimitsWithCustomPlan,
    checkAccountLimits
};

// Call the function to check account limits (only if running directly)
if (require.main === module) {
    checkAccountLimits();
}