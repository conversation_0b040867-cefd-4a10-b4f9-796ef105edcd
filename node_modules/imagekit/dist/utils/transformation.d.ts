import { UrlOptions, TransformationPosition } from "../libs/interfaces";
declare const _default: {
    getDefault: () => TransformationPosition;
    addAsQueryParameter: (options: UrlOptions) => boolean;
    getTransformKey: (transform: string) => string;
    getChainTransformDelimiter: () => string;
    getTransformDelimiter: () => string;
    getTransformKeyValueDelimiter: () => string;
};
export default _default;
