{"name": "imagekit", "version": "6.0.0", "description": "Offical NodeJS SDK for ImageKit.io integration", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"compile": "rm -rf dist/ & tsc -p tsconfig.json", "test": "export NODE_ENV=test; nyc ./node_modules/mocha/bin/mocha --exit -t 40000 tests/*.js;ex=$?;unset NODE_ENV; exit $ex;", "test-e2e": "sh test-e2e.sh; exit $?;", "report-coverage": "nyc report --reporter=text-lcov > coverage.lcov && codecov", "prepack": "npm run compile"}, "author": "ImageKit Developer <<EMAIL>>", "homepage": "https://imagekit.io", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/imagekit-developer/imagekit-nodejs.git"}, "bugs": {"url": "https://github.com/imagekit-developer/imagekit-nodejs/issues"}, "keywords": ["imagekit", "nodejs", "javascript", "sdk", "js", "sdk", "image", "optimization", "image", "transformation", "image", "resize"], "dependencies": {"axios": "^1.6.5", "form-data": "^4.0.0", "hamming-distance": "^1.0.0", "lodash": "^4.17.15", "tslib": "^2.4.0", "uuid": "^8.3.2"}, "engines": {"node": ">=12.0.0"}, "devDependencies": {"@babel/cli": "^7.14.5", "@babel/core": "^7.14.6", "@babel/node": "^7.14.5", "@babel/preset-env": "^7.14.5", "@babel/preset-typescript": "^7.14.5", "@babel/register": "^7.14.5", "@types/chai": "^4.3.1", "@types/lodash": "^4.14.170", "@types/mocha": "^9.1.1", "@types/node": "^15.12.2", "@types/request": "^2.48.5", "@types/sinon": "^10.0.12", "@types/uuid": "^8.3.4", "babel-plugin-replace-ts-export-assignment": "^0.0.2", "chai": "^4.2.0", "codecov": "^3.8.0", "concurrently": "6.5.1", "mocha": "^8.1.1", "nock": "^13.2.7", "nyc": "^15.1.0", "sinon": "^9.2.0", "typescript": "^4.3.2"}}