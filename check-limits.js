// Complete example showing how to check ImageKit account limits and remaining quota
const { getAccountLimitsWithCustomPlan } = require('./server.js');
const config = require('./imagekit-config.js');

async function checkImageKitLimits() {
    try {
        console.log('🔍 Checking ImageKit Account Limits...\n');
        
        // Get account limits using the configuration
        const accountInfo = await getAccountLimitsWithCustomPlan(config.planLimits);
        
        // Display comprehensive account information
        console.log('📊 ACCOUNT USAGE SUMMARY');
        console.log('=' .repeat(50));
        console.log(`Plan: ${accountInfo.planLimits.planName}`);
        console.log(`Period: ${accountInfo.period.startDate} to ${accountInfo.period.endDate}`);
        console.log('');
        
        // Bandwidth Information
        console.log('🌐 BANDWIDTH');
        console.log(`  Used: ${accountInfo.usageFormatted.bandwidthGB} GB (${accountInfo.usagePercentage.bandwidth}%)`);
        console.log(`  Limit: ${accountInfo.planLimits.bandwidthGB} GB`);
        console.log(`  Remaining: ${accountInfo.remaining.bandwidthGB} GB`);
        
        // Bandwidth status
        const bandwidthPercent = parseFloat(accountInfo.usagePercentage.bandwidth);
        if (bandwidthPercent >= config.alerts.bandwidthCriticalPercent) {
            console.log(`  Status: 🔴 CRITICAL - ${bandwidthPercent}% used!`);
        } else if (bandwidthPercent >= config.alerts.bandwidthWarningPercent) {
            console.log(`  Status: 🟡 WARNING - ${bandwidthPercent}% used`);
        } else {
            console.log(`  Status: 🟢 OK - ${bandwidthPercent}% used`);
        }
        console.log('');
        
        // Storage Information
        console.log('💾 STORAGE');
        console.log(`  Used: ${accountInfo.usageFormatted.storageMB} MB (${accountInfo.usagePercentage.storage}%)`);
        console.log(`  Limit: ${accountInfo.planLimits.storageGB} GB`);
        console.log(`  Remaining: ${accountInfo.remaining.storageGB} GB`);
        
        // Storage status
        const storagePercent = parseFloat(accountInfo.usagePercentage.storage);
        if (storagePercent >= config.alerts.storageCriticalPercent) {
            console.log(`  Status: 🔴 CRITICAL - ${storagePercent}% used!`);
        } else if (storagePercent >= config.alerts.storageWarningPercent) {
            console.log(`  Status: 🟡 WARNING - ${storagePercent}% used`);
        } else {
            console.log(`  Status: 🟢 OK - ${storagePercent}% used`);
        }
        console.log('');
        
        // Additional Usage
        console.log('🎬 OTHER USAGE');
        console.log(`  Video Processing Units: ${accountInfo.usageFormatted.videoProcessingUnits}`);
        console.log(`  Extension Units: ${accountInfo.usageFormatted.extensionUnits}`);
        console.log(`  Original Cache Storage: ${accountInfo.usageFormatted.originalCacheStorageMB} MB`);
        console.log('');
        
        // Recommendations
        console.log('💡 RECOMMENDATIONS');
        console.log('=' .repeat(50));
        
        if (bandwidthPercent > 70) {
            console.log('⚠️  Consider optimizing images or upgrading your plan for bandwidth');
        }
        
        if (storagePercent > 70) {
            console.log('⚠️  Consider cleaning up unused files or upgrading your plan for storage');
        }
        
        if (bandwidthPercent < 50 && storagePercent < 50) {
            console.log('✅ Your usage is well within limits');
        }
        
        console.log('\n📝 Note: Update your plan limits in imagekit-config.js for accurate calculations');
        
        return accountInfo;
        
    } catch (error) {
        console.error('❌ Error checking ImageKit limits:', error.message);
        throw error;
    }
}

// Run the check
if (require.main === module) {
    checkImageKitLimits()
        .then(() => {
            console.log('\n✅ Limit check completed successfully');
        })
        .catch((error) => {
            console.error('\n❌ Limit check failed:', error.message);
            process.exit(1);
        });
}

module.exports = { checkImageKitLimits };
