"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// import packages
var hamming_distance_1 = __importDefault(require("hamming-distance"));
// import constants
var errorMessages_1 = __importDefault(require("../libs/constants/errorMessages"));
// regexp validator
var hexRegExp = new RegExp(/^[0-9a-fA-F]+$/, "i");
var errorHandler = function (error) { return new Error("".concat(error.message, ": ").concat(error.help)); };
var pHashDistance = function (firstHash, secondHash) {
    if (!firstHash || !secondHash) {
        return errorHandler(errorMessages_1.default.MISSING_PHASH_VALUE);
    }
    if (!hexRegExp.test(firstHash) || !hexRegExp.test(secondHash)) {
        return errorHandler(errorMessages_1.default.INVALID_PHASH_VALUE);
    }
    var firstHashString = firstHash.toString();
    var secondHashString = secondHash.toString();
    if (firstHashString.length !== secondHashString.length) {
        return errorHandler(errorMessages_1.default.UNEQUAL_STRING_LENGTH);
    }
    var distance = (0, hamming_distance_1.default)(firstHashString, secondHashString);
    return distance;
};
exports.default = { pHashDistance: pHashDistance };
