// Example of how to use the account usage functions
const { getAccountLimits, getAccountUsage } = require('./server.js');

async function exampleUsage() {
    try {
        console.log('=== Getting Account Limits ===');
        
        // Get current month usage with formatted output
        const limits = await getAccountLimits();
        console.log('Current Month Usage:');
        console.log(`- Bandwidth: ${limits.usageFormatted.bandwidthGB} GB`);
        console.log(`- Storage: ${limits.usageFormatted.storageMB} MB`);
        console.log(`- Video Processing Units: ${limits.usageFormatted.videoProcessingUnits}`);
        console.log(`- Extension Units: ${limits.usageFormatted.extensionUnits}`);
        console.log(`- Period: ${limits.period.startDate} to ${limits.period.endDate}`);
        
        console.log('\n=== Getting Custom Date Range Usage ===');
        
        // Get usage for a specific date range
        const customUsage = await getAccountUsage('2025-08-01', '2025-08-31');
        console.log('August 2025 Usage:');
        console.log(`- Bandwidth: ${(customUsage.bandwidthBytes / (1024 * 1024 * 1024)).toFixed(2)} GB`);
        console.log(`- Storage: ${(customUsage.mediaLibraryStorageBytes / (1024 * 1024)).toFixed(2)} MB`);
        
    } catch (error) {
        console.error('Error:', error.message);
    }
}

// Run the example
exampleUsage();
