// Example of how to use the account usage functions
const { getAccountLimits, getAccountUsage, getAccountLimitsWithCustomPlan } = require('./server.js');

async function exampleUsage() {
    try {
        console.log('=== Getting Account Limits (Auto-detected) ===');

        // Get current month usage with auto-detected limits
        const limits = await getAccountLimits();
        console.log('Current Month Usage:');
        console.log(`- Bandwidth: ${limits.usageFormatted.bandwidthGB} GB`);
        console.log(`- Storage: ${limits.usageFormatted.storageMB} MB`);
        console.log(`- Estimated Plan: ${limits.estimatedLimits.estimatedPlan}`);
        console.log(`- Bandwidth Remaining: ${limits.remaining.bandwidthGB} GB`);

        console.log('\n=== Getting Account Limits (Custom Plan) ===');

        // If you know your exact plan limits, you can specify them
        const customPlanLimits = {
            bandwidthGB: 100, // Your actual bandwidth limit in GB
            storageGB: 100,   // Your actual storage limit in GB
            planName: 'Basic Plan' // Optional plan name
        };

        const customLimits = await getAccountLimitsWithCustomPlan(customPlanLimits);
        console.log('With Custom Plan Limits:');
        console.log(`- Plan: ${customLimits.planLimits.planName}`);
        console.log(`- Bandwidth Used: ${customLimits.usageFormatted.bandwidthGB} GB (${customLimits.usagePercentage.bandwidth}%)`);
        console.log(`- Storage Used: ${customLimits.usageFormatted.storageMB} MB (${customLimits.usagePercentage.storage}%)`);
        console.log(`- Bandwidth Remaining: ${customLimits.remaining.bandwidthGB} GB`);
        console.log(`- Storage Remaining: ${customLimits.remaining.storageGB} GB`);

        console.log('\n=== Getting Custom Date Range Usage ===');

        // Get usage for a specific date range
        const customUsage = await getAccountUsage('2025-08-01', '2025-08-31');
        console.log('August 2025 Usage:');
        console.log(`- Bandwidth: ${(customUsage.bandwidthBytes / (1024 * 1024 * 1024)).toFixed(2)} GB`);
        console.log(`- Storage: ${(customUsage.mediaLibraryStorageBytes / (1024 * 1024)).toFixed(2)} MB`);

    } catch (error) {
        console.error('Error:', error.message);
    }
}

// Run the example
exampleUsage();
