# ImageKit Account Limits & Usage Tracker

This project provides a comprehensive solution to track your ImageKit.io account usage and remaining quota, including bandwidth, storage, and other limits.

## 🚀 Features

- ✅ **Real Usage Data**: Get actual usage from ImageKit API
- ✅ **Remaining Quota**: Calculate how much bandwidth/storage you have left
- ✅ **Custom Plan Limits**: Set your actual plan limits for accurate calculations
- ✅ **Usage Alerts**: Visual indicators when approaching limits
- ✅ **Multiple Time Periods**: Check usage for different date ranges
- ✅ **Comprehensive Reporting**: Detailed usage breakdown with recommendations

## 📁 Files Overview

- `server.js` - Main implementation with ImageKit API integration
- `imagekit-config.js` - Configuration file for your plan limits
- `check-limits.js` - Comprehensive limit checker with alerts
- `usage-example.js` - Basic usage examples
- `README-LIMITS.md` - This documentation

## 🔧 Setup

1. **Install Dependencies** (if not already done):
   ```bash
   npm install imagekit
   ```

2. **Configure Your Plan Limits**:
   Edit `imagekit-config.js` and set your actual plan limits:
   ```javascript
   planLimits: {
       bandwidthGB: 100, // Your actual bandwidth limit
       storageGB: 100,   // Your actual storage limit
       planName: 'Basic Plan'
   }
   ```

3. **Update API Keys** (if needed):
   Make sure your ImageKit API keys are correctly set in `server.js`

## 🎯 Quick Start

### Check Your Current Limits
```bash
node check-limits.js
```

This will show you:
- Current usage vs limits
- Remaining quota
- Usage percentages
- Status indicators (🟢 OK, 🟡 WARNING, 🔴 CRITICAL)
- Recommendations

### Basic Usage Examples
```bash
node usage-example.js
```

### Run the Original Server
```bash
node server.js
```

## 📊 Available Functions

### `getAccountUsage(startDate, endDate)`
Get raw usage data for a specific date range.
```javascript
const usage = await getAccountUsage('2025-09-01', '2025-09-07');
```

### `getAccountLimitsWithCustomPlan(customLimits)`
Get usage with remaining quota calculations.
```javascript
const limits = await getAccountLimitsWithCustomPlan({
    bandwidthGB: 100,
    storageGB: 100,
    planName: 'Basic Plan'
});
```

### `checkImageKitLimits()`
Comprehensive limit check with alerts and recommendations.
```javascript
const { checkImageKitLimits } = require('./check-limits.js');
await checkImageKitLimits();
```

## 📈 Usage Data Available

- **Bandwidth Usage** (bytes/GB)
- **Media Library Storage** (bytes/MB/GB)
- **Video Processing Units**
- **Extension Units**
- **Original Cache Storage**

## ⚙️ Configuration Options

### Plan Limits (imagekit-config.js)
```javascript
planLimits: {
    bandwidthGB: 20,    // Monthly bandwidth limit
    storageGB: 20,      // Total storage limit
    planName: 'Free Plan'
}
```

### Alert Thresholds
```javascript
alerts: {
    bandwidthWarningPercent: 80,  // Yellow alert at 80%
    storageWarningPercent: 80,
    bandwidthCriticalPercent: 95, // Red alert at 95%
    storageCriticalPercent: 95
}
```

## 🎨 Sample Output

```
📊 ACCOUNT USAGE SUMMARY
==================================================
Plan: Basic Plan
Period: 2025-09-01 to 2025-09-07

🌐 BANDWIDTH
  Used: 0.07 GB (0.1%)
  Limit: 100 GB
  Remaining: 99.93 GB
  Status: 🟢 OK - 0.1% used

💾 STORAGE
  Used: 13.53 MB (0.0%)
  Limit: 100 GB
  Remaining: 99.99 GB
  Status: 🟢 OK - 0.0% used
```

## 🔍 Common ImageKit Plans

| Plan | Bandwidth | Storage | Price |
|------|-----------|---------|-------|
| Free | 20 GB | 20 GB | $0 |
| Starter | 40 GB | 40 GB | ~$20/month |
| Basic | 100 GB | 100 GB | ~$50/month |
| Standard | 250 GB | 250 GB | ~$100/month |
| Pro | 500 GB | 500 GB | ~$200/month |

## ⚠️ Important Notes

1. **API Limitations**: ImageKit doesn't provide plan limit information via API, so you need to manually configure your limits
2. **Date Range Required**: The usage API requires both start and end dates
3. **Monthly Billing**: Most plans are billed monthly, so track usage accordingly
4. **Real-time Data**: Usage data may have a slight delay (usually a few minutes)

## 🛠️ Troubleshooting

### "startDate and endDate are required"
Make sure you're providing both dates in YYYY-MM-DD format.

### "Authentication failed"
Check your private API key in `server.js`.

### Incorrect remaining quota
Update your plan limits in `imagekit-config.js` with your actual plan details.

## 📝 License

This is a utility script for ImageKit.io usage tracking. Make sure to comply with ImageKit's terms of service.
