"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var respond_1 = __importDefault(require("./respond"));
var axios_1 = __importDefault(require("axios"));
// constant
var UnknownError = "Unknown error occured";
function request(requestOptions, defaultOptions, callback) {
    var options = {
        method: requestOptions.method,
        url: requestOptions.url,
        auth: {
            username: defaultOptions.privateKey || "",
            password: "",
        },
        maxBodyLength: Infinity,
    };
    if (typeof requestOptions.json === "object")
        options.data = requestOptions.json;
    else if (typeof requestOptions.formData === "object")
        options.data = requestOptions.formData;
    if (typeof requestOptions.qs === "object")
        options.params = requestOptions.qs;
    if (typeof requestOptions.headers === "object")
        options.headers = requestOptions.headers;
    (0, axios_1.default)(options).then(function (response) {
        if (typeof callback !== "function")
            return;
        var data = response.data, status = response.status, headers = response.headers;
        var responseMetadata = {
            statusCode: status,
            headers: headers.toJSON()
        };
        var result = data ? data : {};
        // define status code and headers as non-enumerable properties on data
        Object.defineProperty(result, "$ResponseMetadata", {
            value: responseMetadata,
            enumerable: false,
            writable: false
        });
        (0, respond_1.default)(false, result, callback);
    }, function (error) {
        if (typeof callback !== "function")
            return;
        if (error.response) {
            // The request was made and the server responded with a status code
            // that falls out of the range of 2xx
            var responseMetadata = {
                statusCode: error.response.status,
                headers: error.response.headers.toJSON()
            };
            var result = {};
            if (error.response.data && typeof error.response.data === "object") {
                result = error.response.data;
            }
            else if (error.response.data && typeof error.response.data === "string") {
                result = {
                    help: error.response.data
                };
            }
            if (error.response.status === 429) {
                result = __assign(__assign({}, result), { "X-RateLimit-Limit": parseInt(error.response.headers["x-ratelimit-limit"], 10), "X-RateLimit-Reset": parseInt(error.response.headers["x-ratelimit-reset"], 10), "X-RateLimit-Interval": parseInt(error.response.headers["x-ratelimit-interval"], 10) });
            }
            // define status code and headers as non-enumerable properties on data
            Object.defineProperty(result, "$ResponseMetadata", {
                value: responseMetadata,
                enumerable: false,
                writable: false
            });
            (0, respond_1.default)(true, result, callback);
        }
        else if (error) {
            (0, respond_1.default)(true, error, callback);
            // The request was made but no response was received
            // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
            // http.ClientRequest in node.js
        }
        else {
            (0, respond_1.default)(true, new Error(UnknownError), callback);
        }
    });
}
exports.default = request;
