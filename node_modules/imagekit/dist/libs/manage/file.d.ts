import { IKCallback } from "../interfaces/IKCallback";
import { ImageKitOptions, ListFileOptions, FileDetailsOptions, FileVersionDetailsOptions, FileObject, FileMetadataResponse, BulkDeleteFilesResponse, BulkDeleteFilesError, CopyFileOptions, CopyFolderResponse, MoveFileOptions, CreateFolderOptions, CopyFolderOptions, MoveFolderOptions, DeleteFileVersionOptions, RestoreFileVersionOptions, RenameFileOptions, RenameFileResponse } from "../interfaces";
declare const _default: {
    deleteFile: (fileId: string, defaultOptions: ImageKitOptions, callback?: IKCallback<void, Error> | undefined) => void;
    getMetadata: (fileIdOrURL: string, defaultOptions: ImageKitOptions, callback?: IKCallback<FileMetadataResponse, Error> | undefined) => void;
    getDetails: (fileId: string, defaultOptions: ImageKitOptions, callback?: IKCallback<FileObject, Error> | undefined) => void;
    getFileVersionDetails: (fileDetailsOptions: FileVersionDetailsOptions, defaultOptions: ImageKitOptions, callback?: IKCallback<FileObject, Error> | undefined) => void;
    updateDetails: (fileId: string, updateData: FileDetailsOptions, defaultOptions: ImageKitOptions, callback?: IKCallback<FileObject, Error> | undefined) => void;
    listFiles: (listOptions: ListFileOptions, defaultOptions: ImageKitOptions, callback?: IKCallback<FileObject[], Error> | undefined) => void;
    getFilesVersions: (fileId: string, defaultOptions: ImageKitOptions, callback?: IKCallback<FileObject[], Error> | undefined) => void;
    bulkDeleteFiles: (fileIdArray: string[], defaultOptions: ImageKitOptions, callback?: IKCallback<BulkDeleteFilesResponse, BulkDeleteFilesError> | undefined) => void;
    deleteFileVersion: (deleteFileVersionOptions: DeleteFileVersionOptions, defaultOptions: ImageKitOptions, callback?: IKCallback<void, Error> | undefined) => void;
    restoreFileVersion: (restoreFileVersionOptions: RestoreFileVersionOptions, defaultOptions: ImageKitOptions, callback?: IKCallback<FileObject, Error> | undefined) => void;
    bulkAddTags: (fileIdArray: string[], tags: string[], defaultOptions: ImageKitOptions, callback?: IKCallback<void, Error> | undefined) => void;
    bulkRemoveTags: (fileIdArray: string[], tags: string[], defaultOptions: ImageKitOptions, callback?: IKCallback<void, Error> | undefined) => void;
    bulkRemoveAITags: (fileIdArray: string[], tags: string[], defaultOptions: ImageKitOptions, callback?: IKCallback<void, Error> | undefined) => void;
    copyFile: (copyFileOptions: CopyFileOptions, defaultOptions: ImageKitOptions, callback?: IKCallback<void, Error> | undefined) => void;
    moveFile: (moveFileOptions: MoveFileOptions, defaultOptions: ImageKitOptions, callback?: IKCallback<void, Error> | undefined) => void;
    renameFile: (renameFileOptions: RenameFileOptions, defaultOptions: ImageKitOptions, callback?: IKCallback<RenameFileResponse, Error> | undefined) => void;
    copyFolder: (copyFolderOptions: CopyFolderOptions, defaultOptions: ImageKitOptions, callback?: IKCallback<CopyFolderResponse, Error> | undefined) => void;
    moveFolder: (moveFolderOptions: MoveFolderOptions, defaultOptions: ImageKitOptions, callback?: IKCallback<void, Error> | undefined) => void;
    createFolder: (createFolderOptions: CreateFolderOptions, defaultOptions: ImageKitOptions, callback?: IKCallback<void, Error> | undefined) => void;
    deleteFolder: (folderPath: string, defaultOptions: ImageKitOptions, callback?: IKCallback<void, Error> | undefined) => void;
    getBulkJobStatus: (jobId: string, defaultOptions: ImageKitOptions, callback?: IKCallback<void, Error> | undefined) => void;
};
export default _default;
