import { BulkDeleteFilesError, BulkDeleteFilesResponse, CopyFolderError, CopyFolderResponse, FileDetailsOptions, FileObject, FileMetadataResponse, ImageKitOptions, ListFileOptions, ListFileResponse, MoveFolderError, MoveFolderResponse, PurgeCacheResponse, PurgeCacheStatusResponse, UploadOptions, UploadResponse, UrlOptions, CopyFileOptions, MoveFileOptions, CreateFolderOptions, CopyFolderOptions, MoveFolderOptions, FileVersionDetailsOptions, DeleteFileVersionOptions, RestoreFileVersionOptions, CreateCustomMetadataFieldOptions, GetCustomMetadataFieldsOptions, CustomMetadataField, UpdateCustomMetadataFieldOptions, RenameFileOptions, RenameFileResponse } from "./libs/interfaces";
import { IKCallback } from "./libs/interfaces/IKCallback";
import IKResponse from "./libs/interfaces/IKResponse";
declare class ImageKit {
    options: ImageKitOptions;
    constructor(opts?: ImageKitOptions);
    /**
     * This method allows you to create an URL to access a file using the relative or absolute path and the ImageKit URL endpoint (urlEndpoint). The file can be an image, video or any other static file supported by ImageKit.
     *
     * @see {@link https://github.com/imagekit-developer/imagekit-nodejs#url-generation}
     * @see {@link https://docs.imagekit.io/integration/url-endpoints}
     *
     * @param urlOptions
     */
    url(urlOptions: UrlOptions): string;
    /**
     * You can upload file to ImageKit.io media library from your server-side using private API key authentication.
     *
     * @see {@link https://docs.imagekit.io/api-reference/upload-file-api/server-side-file-upload}
     *
     * @param uploadOptions
     */
    upload(uploadOptions: UploadOptions): Promise<IKResponse<UploadResponse>>;
    upload(uploadOptions: UploadOptions, callback: IKCallback<IKResponse<UploadResponse>>): void;
    /**
     * This API can list all the uploaded files in your ImageKit.io media library.
     * For searching and filtering, you can use query parameters as described in docs.
     *
     * @see {@link https://docs.imagekit.io/api-reference/media-api/list-and-search-files}
     *
     * @param listFilesOptions
     */
    listFiles(listOptions: ListFileOptions): Promise<IKResponse<ListFileResponse>>;
    listFiles(listOptions: ListFileOptions, callback: IKCallback<IKResponse<ListFileResponse>>): void;
    /**
     * Get the file details such as tags, customCoordinates, and isPrivate properties using get file detail API.
     *
     * @see {@link https://docs.imagekit.io/api-reference/media-api/get-file-details}
     *
     * @param fileId
     */
    getFileDetails(fileId: string): Promise<IKResponse<FileObject>>;
    getFileDetails(fileId: string, callback: IKCallback<IKResponse<FileObject>>): void;
    /**
     * Get all versions of an assset.
     *
     * @see {@link https://docs.imagekit.io/api-reference/media-api/get-file-versions}
     *
     * @param fileId
     */
    getFileVersions(fileId: string): Promise<IKResponse<FileObject>>;
    getFileVersions(fileId: string, callback: IKCallback<IKResponse<FileObject>>): void;
    /**
     * Get file details of a specific version.
     *
     * @see {@link https://docs.imagekit.io/api-reference/media-api/get-file-version-details}
     *
     * @param fileVersionDetailsOptions
     */
    getFileVersionDetails(fileVersionDetailsOptions: FileVersionDetailsOptions): Promise<IKResponse<FileObject>>;
    getFileVersionDetails(fileVersionDetailsOptions: FileVersionDetailsOptions, callback: IKCallback<IKResponse<FileObject>>): void;
    /**
     * Get image exif, pHash and other metadata for uploaded files in ImageKit.io media library using this API.
     *
     * @see {@link https://docs.imagekit.io/api-reference/metadata-api/get-image-metadata-for-uploaded-media-files}
     *
     * @param fileIdOrURL The unique fileId of the uploaded file or absolute URL.
     */
    getFileMetadata(fileIdOrURL: string): Promise<IKResponse<FileMetadataResponse>>;
    getFileMetadata(fileIdOrURL: string, callback: IKCallback<IKResponse<FileMetadataResponse>>): void;
    /**
     * Update file details such as tags and customCoordinates attribute using update file detail API.
     *
     * @see {@link https://docs.imagekit.io/api-reference/media-api/update-file-details}
     *
     * @param fileId The unique fileId of the uploaded file. fileId is returned in list files API and upload API.
     * @param updateData
     */
    updateFileDetails(fileId: string, updateData: FileDetailsOptions): Promise<IKResponse<FileObject>>;
    updateFileDetails(fileId: string, updateData: FileDetailsOptions, callback: IKCallback<IKResponse<FileObject>>): void;
    /**
     * Add tags to multiple files in a single request. The method accepts an array of fileIDs of the files and an array of tags that have to be added to those files.
     *
     * @see {@link https://docs.imagekit.io/api-reference/media-api/add-tags-bulk}
     *
     * @param fileIds
     * @param tags
     */
    bulkAddTags(fileIds: string[], tags: string[]): Promise<IKResponse<void>>;
    bulkAddTags(fileIds: string[], tags: string[], callback: IKCallback<IKResponse<void>>): void;
    /**
     * Remove tags to multiple files in a single request. The method accepts an array of fileIDs of the files and an array of tags that have to be removed to those files.
     *
     * @see {@link https://docs.imagekit.io/api-reference/media-api/remove-tags-bulk}
     *
     * @param fileIds
     * @param tags
     */
    bulkRemoveTags(fileIds: string[], tags: string[]): Promise<IKResponse<void>>;
    bulkRemoveTags(fileIds: string[], tags: string[], callback: IKCallback<IKResponse<void>>): void;
    /**
     * Remove AITags from multiple files in a single request.
     *
     * @see {@link https://docs.imagekit.io/api-reference/media-api/remove-aitags-bulk}
     *
     * @param fileIds
     * @param tags
     */
    bulkRemoveAITags(fileIds: string[], tags: string[]): Promise<IKResponse<void>>;
    bulkRemoveAITags(fileIds: string[], tags: string[], callback: IKCallback<IKResponse<void>>): void;
    /**
     * You can programmatically delete uploaded files in media library using delete file API.
     *
     * @see {@link https://docs.imagekit.io/api-reference/media-api/delete-file}
     *
     * @param fileId The unique fileId of the uploaded file. fileId is returned in list files API and upload API
     */
    deleteFile(fileId: string): Promise<IKResponse<void>>;
    deleteFile(fileId: string, callback: IKCallback<IKResponse<void>>): void;
    /**
     * Delete any non-current version of a file.
     *
     * @see {@link https://docs.imagekit.io/api-reference/media-api/delete-file-version}
     *
     * @param deleteFileVersionOptions
     */
    deleteFileVersion(deleteFileVersionOptions: DeleteFileVersionOptions): Promise<IKResponse<void>>;
    deleteFileVersion(deleteFileVersionOptions: DeleteFileVersionOptions, callback: IKCallback<IKResponse<void>>): void;
    /**
     * Restore file version to a different version of a file.
     *
     * @see {@link https://docs.imagekit.io/api-reference/media-api/restore-file-version}
     *
     * @param restoreFileVersionOptions
     */
    restoreFileVersion(restoreFileVersionOptions: RestoreFileVersionOptions): Promise<IKResponse<FileObject>>;
    restoreFileVersion(restoreFileVersionOptions: RestoreFileVersionOptions, callback: IKCallback<IKResponse<FileObject>>): void;
    /**
     * This will purge CDN and ImageKit.io internal cache.
     *
     * @see {@link https://docs.imagekit.io/api-reference/media-api/purge-cache}
     *
     * @param url The exact URL of the file to be purged. For example - https://ik.imageki.io/your_imagekit_id/rest-of-the-file-path.jpg
     */
    purgeCache(url: string): Promise<IKResponse<PurgeCacheResponse>>;
    purgeCache(url: string, callback: IKCallback<IKResponse<PurgeCacheResponse>>): void;
    /**
     * Get the status of submitted purge request.
     *
     * @see {@link https://docs.imagekit.io/api-reference/media-api/purge-cache-status}
     *
     * @param requestId The requestId returned in response of purge cache API.
     */
    getPurgeCacheStatus(requestId: string, callback: IKCallback<IKResponse<PurgeCacheStatusResponse>>): void;
    getPurgeCacheStatus(requestId: string): Promise<IKResponse<PurgeCacheStatusResponse>>;
    /**
     * Delete multiple files. The method accepts an array of file IDs of the files that have to be deleted.
     *
     * @see {@link https://docs.imagekit.io/api-reference/media-api/delete-files-bulk}
     *
     * @param fileIdArray The requestId returned in response of purge cache API.
     */
    bulkDeleteFiles(fileIdArray: string[], callback?: IKCallback<IKResponse<BulkDeleteFilesResponse>, IKResponse<BulkDeleteFilesError>>): void | Promise<IKResponse<BulkDeleteFilesResponse>>;
    /**
     * This will copy a file from one location to another. This method accepts the source file's path and destination folder path.
     *
     * @see {@link https://docs.imagekit.io/api-reference/media-api/copy-file}
     *
     * @param copyFileOptions
     */
    copyFile(copyFileOptions: CopyFileOptions): Promise<IKResponse<void>>;
    copyFile(copyFileOptions: CopyFileOptions, callback: IKCallback<IKResponse<void>>): void;
    /**
     * This will move a file from one location to another. This method accepts the source file's path and destination folder path.
     *
     * @see {@link https://docs.imagekit.io/api-reference/media-api/move-file}
     *
     * @param moveFileOptions
     */
    moveFile(moveFileOptions: MoveFileOptions): Promise<IKResponse<void>>;
    moveFile(moveFileOptions: MoveFileOptions, callback: IKCallback<IKResponse<void>>): void;
    /**
     * You can programmatically rename an already existing file in the media library using rename file API. This operation would rename all file versions of the file. Note: The old URLs will stop working. The file/file version URLs cached on CDN will continue to work unless a purge is requested.
     *
     * @see {@link https://docs.imagekit.io/api-reference/media-api/rename-file}
     *
     * @param renameFileOptions
     */
    renameFile(renameFileOptions: RenameFileOptions): Promise<IKResponse<RenameFileResponse>>;
    renameFile(renameFileOptions: RenameFileOptions, callback: IKCallback<IKResponse<RenameFileResponse>>): void;
    /**
     * This will create a new folder. This method accepts folder name and parent folder path.
     *
     * @see {@link https://docs.imagekit.io/api-reference/media-api/create-folder}
     *
     * @param createFolderOptions
     */
    createFolder(createFolderOptions: CreateFolderOptions): Promise<IKResponse<void>>;
    createFolder(createFolderOptions: CreateFolderOptions, callback: IKCallback<IKResponse<void>>): void;
    /**
     * This will delete the specified folder and all nested files & folders. This method accepts the full path of the folder that is to be deleted.
     *
     * @see {@link https://docs.imagekit.io/api-reference/media-api/delete-folder}
     *
     * @param foldePath
     */
    deleteFolder(folderPath: string): Promise<IKResponse<void>>;
    deleteFolder(folderPath: string, callback: IKCallback<IKResponse<void>>): void;
    /**
     * This will copy a folder from one location to another. This method accepts the source folder's path and destination folder path.
     *
     * @see {@link https://docs.imagekit.io/api-reference/media-api/copy-folder}
     *
     * @param copyFolderOptions
     */
    copyFolder(copyFolderOptions: CopyFolderOptions): Promise<IKResponse<CopyFolderResponse>>;
    copyFolder(copyFolderOptions: CopyFolderOptions, callback: IKCallback<IKResponse<CopyFolderResponse>, IKResponse<CopyFolderError>>): void;
    /**
     * This will move a folder from one location to another. This method accepts the source folder's path and destination folder path.
     *
     * @see {@link https://docs.imagekit.io/api-reference/media-api/move-folder}
     *
     * @param moveFolderOptions
     */
    moveFolder(moveFolderOptions: MoveFolderOptions): Promise<IKResponse<MoveFolderResponse>>;
    moveFolder(moveFolderOptions: MoveFolderOptions, callback: IKCallback<IKResponse<MoveFolderResponse>, IKResponse<MoveFolderError>>): void;
    /**
     * In case you are looking to implement client-side file upload, you are going to need a token, expiry timestamp, and a valid signature for that upload. The SDK provides a simple method that you can use in your code to generate these authentication parameters for you.
     *
     * @see {@link https://github.com/imagekit-developer/imagekit-nodejs#authentication-parameter-generation}
     *
     * @param token
     * @param expire
     */
    getAuthenticationParameters(token?: string, expire?: number): {
        token: string;
        expire: number;
        signature: string;
    };
    /**
     * This allows us to get a bulk operation status e.g. copy or move folder. This method accepts jobId that is returned by copy and move folder operations.
     *
     * @see {@link https://docs.imagekit.io/api-reference/media-api/move-folder}
     *
     * @param jobId
     */
    getBulkJobStatus(jobId: string): Promise<IKResponse<void>>;
    getBulkJobStatus(jobId: string, callback: IKCallback<IKResponse<void>>): Promise<IKResponse<void>>;
    /**
     * Create custom metadata field
     *
     * @see {@link https://docs.imagekit.io/api-reference/custom-metadata-fields-api/create-custom-metadata-field}
     *
     * @param createCustomMetadataFieldOptions
     */
    createCustomMetadataField(createCustomMetadataFieldOptions: CreateCustomMetadataFieldOptions): Promise<IKResponse<CustomMetadataField>>;
    createCustomMetadataField(createCustomMetadataFieldOptions: CreateCustomMetadataFieldOptions, callback: IKCallback<IKResponse<CustomMetadataField>>): Promise<IKResponse<CustomMetadataField>>;
    /**
     *Get a list of all the custom metadata fields.
     *
     * @see {@link https://docs.imagekit.io/api-reference/custom-metadata-fields-api/get-custom-metadata-field}
     *
     */
    getCustomMetadataFields(getCustomMetadataFieldsOptions: GetCustomMetadataFieldsOptions): Promise<IKResponse<CustomMetadataField[]>>;
    getCustomMetadataFields(getCustomMetadataFieldsOptions: GetCustomMetadataFieldsOptions, callback: IKCallback<IKResponse<CustomMetadataField[]>>): Promise<IKResponse<CustomMetadataField[]>>;
    /**
     * Update custom metadata field
     *
     * @see {@link https://docs.imagekit.io/api-reference/custom-metadata-fields-api/update-custom-metadata-field}
     *
     * @param fieldId
     * @param updateCustomMetadataFieldOptions
     */
    updateCustomMetadataField(fieldId: string, updateCustomMetadataFieldOptions: UpdateCustomMetadataFieldOptions): Promise<IKResponse<CustomMetadataField>>;
    updateCustomMetadataField(fieldId: string, updateCustomMetadataFieldOptions: UpdateCustomMetadataFieldOptions, callback: IKCallback<IKResponse<CustomMetadataField>>): Promise<IKResponse<CustomMetadataField>>;
    /**
     * Delete a custom metadata field
     *
     * @see {@link https://docs.imagekit.io/api-reference/custom-metadata-fields-api/delete-custom-metadata-field}
     *
     * @param fieldId
     */
    deleteCustomMetadataField(fieldId: string): Promise<IKResponse<void>>;
    deleteCustomMetadataField(fieldId: string, callback: IKCallback<IKResponse<void>>): void;
    /**
     * Perceptual hashing allows you to construct a hash value that uniquely identifies an input image based on an image's contents. ImageKit.io metadata API returns the pHash value of an image in the response. You can use this value to find a duplicate (or similar) image by calculating the distance between the two images' pHash value.
     *
     * This SDK exposes pHashDistance function to calculate the distance between two pHash values. It accepts two pHash hexadecimal strings and returns a numeric value indicative of the level of difference between the two images.
     *
     * @see {@link https://docs.imagekit.io/api-reference/metadata-api#perceptual-hash-phash}
     *
     * @param firstPHash
     * @param secondPHash
     */
    pHashDistance(firstPHash: string, secondPHash: string): number | Error;
    /**
     * @param payload - Raw webhook request body (Encoded as UTF8 string or Buffer)
     * @param signature - Webhook signature as UTF8 encoded strings (Stored in `x-ik-signature` header of the request)
     * @param secret - Webhook secret as UTF8 encoded string [Copy from ImageKit dashboard](https://imagekit.io/dashboard/developer/webhooks)
     * @returns \{ `timestamp`: Verified UNIX epoch timestamp if signature, `event`: Parsed webhook event payload \}
     */
    verifyWebhookEvent: (payload: string | Uint8Array, signature: string, secret: string) => {
        timestamp: number;
        event: import("./libs/interfaces").WebhookEvent;
    };
}
export = ImageKit;
