"use strict";
var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
/*
    Helper Modules
*/
var lodash_1 = __importDefault(require("lodash"));
var errorMessages_1 = __importDefault(require("./libs/constants/errorMessages"));
var manage_1 = __importDefault(require("./libs/manage"));
var signature_1 = __importDefault(require("./libs/signature"));
var upload_1 = __importDefault(require("./libs/upload"));
var webhook_signature_1 = require("./utils/webhook-signature");
var custom_metadata_field_1 = __importDefault(require("./libs/manage/custom-metadata-field"));
/*
    Implementations
*/
var url_1 = __importDefault(require("./libs/url"));
/*
    Utils
*/
var phash_1 = __importDefault(require("./utils/phash"));
var transformation_1 = __importDefault(require("./utils/transformation"));
var promisify = function (thisContext, fn) {
    return function () {
        var args = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            args[_i] = arguments[_i];
        }
        if (args.length === fn.length && typeof args[args.length - 1] !== "undefined") {
            if (typeof args[args.length - 1] !== "function") {
                throw new Error("Callback must be a function.");
            }
            fn.call.apply(fn, __spreadArray([thisContext], __read(args), false));
        }
        else {
            return new Promise(function (resolve, reject) {
                var callback = function (err) {
                    var results = [];
                    for (var _i = 1; _i < arguments.length; _i++) {
                        results[_i - 1] = arguments[_i];
                    }
                    if (err) {
                        return reject(err);
                    }
                    else {
                        resolve(results.length > 1 ? results : results[0]);
                    }
                };
                args.pop();
                args.push(callback);
                fn.call.apply(fn, __spreadArray([thisContext], __read(args), false));
            });
        }
    };
};
var ImageKit = /** @class */ (function () {
    function ImageKit(opts) {
        if (opts === void 0) { opts = {}; }
        this.options = {
            uploadEndpoint: "https://upload.imagekit.io/api/v1/files/upload",
            publicKey: "",
            privateKey: "",
            urlEndpoint: "",
            transformationPosition: transformation_1.default.getDefault(),
        };
        /**
         * @param payload - Raw webhook request body (Encoded as UTF8 string or Buffer)
         * @param signature - Webhook signature as UTF8 encoded strings (Stored in `x-ik-signature` header of the request)
         * @param secret - Webhook secret as UTF8 encoded string [Copy from ImageKit dashboard](https://imagekit.io/dashboard/developer/webhooks)
         * @returns \{ `timestamp`: Verified UNIX epoch timestamp if signature, `event`: Parsed webhook event payload \}
         */
        this.verifyWebhookEvent = webhook_signature_1.verify;
        this.options = lodash_1.default.extend(this.options, opts);
        if (!this.options.publicKey) {
            throw new Error(errorMessages_1.default.MANDATORY_PUBLIC_KEY_MISSING.message);
        }
        if (!this.options.privateKey) {
            throw new Error(errorMessages_1.default.MANDATORY_PRIVATE_KEY_MISSING.message);
        }
        if (!this.options.urlEndpoint) {
            throw new Error(errorMessages_1.default.MANDATORY_URL_ENDPOINT_KEY_MISSING.message);
        }
    }
    /**
     * This method allows you to create an URL to access a file using the relative or absolute path and the ImageKit URL endpoint (urlEndpoint). The file can be an image, video or any other static file supported by ImageKit.
     *
     * @see {@link https://github.com/imagekit-developer/imagekit-nodejs#url-generation}
     * @see {@link https://docs.imagekit.io/integration/url-endpoints}
     *
     * @param urlOptions
     */
    ImageKit.prototype.url = function (urlOptions) {
        return (0, url_1.default)(urlOptions, this.options);
    };
    ImageKit.prototype.upload = function (uploadOptions, callback) {
        return promisify(this, upload_1.default)(uploadOptions, this.options, callback);
    };
    ImageKit.prototype.listFiles = function (listOptions, callback) {
        return promisify(this, manage_1.default.listFiles)(listOptions, this.options, callback);
    };
    ImageKit.prototype.getFileDetails = function (fileId, callback) {
        return promisify(this, manage_1.default.getFileDetails)(fileId, this.options, callback);
    };
    ImageKit.prototype.getFileVersions = function (fileId, callback) {
        return promisify(this, manage_1.default.getFileVersions)(fileId, this.options, callback);
    };
    ImageKit.prototype.getFileVersionDetails = function (fileVersionDetailsOptions, callback) {
        return promisify(this, manage_1.default.getFileVersionDetails)(fileVersionDetailsOptions, this.options, callback);
    };
    ImageKit.prototype.getFileMetadata = function (fileIdOrURL, callback) {
        return promisify(this, manage_1.default.getFileMetadata)(fileIdOrURL, this.options, callback);
    };
    ImageKit.prototype.updateFileDetails = function (fileId, updateData, callback) {
        return promisify(this, manage_1.default.updateFileDetails)(fileId, updateData, this.options, callback);
    };
    ImageKit.prototype.bulkAddTags = function (fileIds, tags, callback) {
        return promisify(this, manage_1.default.bulkAddTags)(fileIds, tags, this.options, callback);
    };
    ImageKit.prototype.bulkRemoveTags = function (fileIds, tags, callback) {
        return promisify(this, manage_1.default.bulkRemoveTags)(fileIds, tags, this.options, callback);
    };
    ImageKit.prototype.bulkRemoveAITags = function (fileIds, tags, callback) {
        return promisify(this, manage_1.default.bulkRemoveAITags)(fileIds, tags, this.options, callback);
    };
    ImageKit.prototype.deleteFile = function (fileId, callback) {
        return promisify(this, manage_1.default.deleteFile)(fileId, this.options, callback);
    };
    ImageKit.prototype.deleteFileVersion = function (deleteFileVersionOptions, callback) {
        return promisify(this, manage_1.default.deleteFileVersion)(deleteFileVersionOptions, this.options, callback);
    };
    ImageKit.prototype.restoreFileVersion = function (restoreFileVersionOptions, callback) {
        return promisify(this, manage_1.default.restoreFileVersion)(restoreFileVersionOptions, this.options, callback);
    };
    ImageKit.prototype.purgeCache = function (url, callback) {
        return promisify(this, manage_1.default.purgeCache)(url, this.options, callback);
    };
    ImageKit.prototype.getPurgeCacheStatus = function (requestId, callback) {
        return promisify(this, manage_1.default.getPurgeCacheStatus)(requestId, this.options, callback);
    };
    ImageKit.prototype.bulkDeleteFiles = function (fileIdArray, callback) {
        return promisify(this, manage_1.default.bulkDeleteFiles)(fileIdArray, this.options, callback);
    };
    ImageKit.prototype.copyFile = function (copyFileOptions, callback) {
        return promisify(this, manage_1.default.copyFile)(copyFileOptions, this.options, callback);
    };
    ImageKit.prototype.moveFile = function (moveFileOptions, callback) {
        return promisify(this, manage_1.default.moveFile)(moveFileOptions, this.options, callback);
    };
    ImageKit.prototype.renameFile = function (renameFileOptions, callback) {
        return promisify(this, manage_1.default.renameFile)(renameFileOptions, this.options, callback);
    };
    ImageKit.prototype.createFolder = function (createFolderOptions, callback) {
        return promisify(this, manage_1.default.createFolder)(createFolderOptions, this.options, callback);
    };
    ImageKit.prototype.deleteFolder = function (folderPath, callback) {
        return promisify(this, manage_1.default.deleteFolder)(folderPath, this.options, callback);
    };
    ImageKit.prototype.copyFolder = function (copyFolderOptions, callback) {
        return promisify(this, manage_1.default.copyFolder)(copyFolderOptions, this.options, callback);
    };
    ImageKit.prototype.moveFolder = function (moveFolderOptions, callback) {
        return promisify(this, manage_1.default.moveFolder)(moveFolderOptions, this.options, callback);
    };
    /**
     * In case you are looking to implement client-side file upload, you are going to need a token, expiry timestamp, and a valid signature for that upload. The SDK provides a simple method that you can use in your code to generate these authentication parameters for you.
     *
     * @see {@link https://github.com/imagekit-developer/imagekit-nodejs#authentication-parameter-generation}
     *
     * @param token
     * @param expire
     */
    ImageKit.prototype.getAuthenticationParameters = function (token, expire) {
        return signature_1.default.getAuthenticationParameters(token, expire, this.options);
    };
    ImageKit.prototype.getBulkJobStatus = function (jobId, callback) {
        return promisify(this, manage_1.default.getBulkJobStatus)(jobId, this.options, callback);
    };
    ImageKit.prototype.createCustomMetadataField = function (createCustomMetadataFieldOptions, callback) {
        return promisify(this, custom_metadata_field_1.default.create)(createCustomMetadataFieldOptions, this.options, callback);
    };
    ImageKit.prototype.getCustomMetadataFields = function (getCustomMetadataFieldsOptions, callback) {
        return promisify(this, custom_metadata_field_1.default.list)(getCustomMetadataFieldsOptions, this.options, callback);
    };
    ImageKit.prototype.updateCustomMetadataField = function (fieldId, updateCustomMetadataFieldOptions, callback) {
        return promisify(this, custom_metadata_field_1.default.update)(fieldId, updateCustomMetadataFieldOptions, this.options, callback);
    };
    ImageKit.prototype.deleteCustomMetadataField = function (fieldId, callback) {
        return promisify(this, custom_metadata_field_1.default.deleteField)(fieldId, this.options, callback);
    };
    /**
     * Perceptual hashing allows you to construct a hash value that uniquely identifies an input image based on an image's contents. ImageKit.io metadata API returns the pHash value of an image in the response. You can use this value to find a duplicate (or similar) image by calculating the distance between the two images' pHash value.
     *
     * This SDK exposes pHashDistance function to calculate the distance between two pHash values. It accepts two pHash hexadecimal strings and returns a numeric value indicative of the level of difference between the two images.
     *
     * @see {@link https://docs.imagekit.io/api-reference/metadata-api#perceptual-hash-phash}
     *
     * @param firstPHash
     * @param secondPHash
     */
    ImageKit.prototype.pHashDistance = function (firstPHash, secondPHash) {
        return phash_1.default.pHashDistance(firstPHash, secondPHash);
    };
    return ImageKit;
}());
module.exports = ImageKit;
