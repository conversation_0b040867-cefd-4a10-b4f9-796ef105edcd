import { IKCallback } from "../interfaces/IKCallback";
import { ImageKitOptions, CreateCustomMetadataFieldOptions, CustomMetadataField, UpdateCustomMetadataFieldOptions, GetCustomMetadataFieldsOptions } from "../interfaces";
declare const _default: {
    create: (createCustomMetadataFieldOptions: CreateCustomMetadataFieldOptions, defaultOptions: ImageKitOptions, callback?: IKCallback<CustomMetadataField, Error> | undefined) => void;
    list: (getCustomMetadataFieldsOptions: GetCustomMetadataFieldsOptions, defaultOptions: ImageKitOptions, callback?: IKCallback<CustomMetadataField[], Error> | undefined) => void;
    update: (fieldId: string, updateCustomMetadataFieldOptions: UpdateCustomMetadataFieldOptions, defaultOptions: ImageKitOptions, callback?: IKCallback<CustomMetadataField, Error> | undefined) => void;
    deleteField: (fieldId: string, defaultOptions: ImageKitOptions, callback?: I<PERSON><PERSON>allback<void, Error> | undefined) => void;
};
export default _default;
