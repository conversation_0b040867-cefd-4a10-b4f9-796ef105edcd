<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
  </head>
  <body>
    
    <script src="https://unpkg.com/imagekit-media-library-widget/dist/imagekit-media-library-widget.min.js"></script>

    <script>
      // SDK initialization

      var imagekit = new ImageKit({
        publicKey: "public_Q8avZAesIfF2SEOFiBwIqukGGXk=",
        urlEndpoint: "https://ik.imagekit.io/aayang",
        authenticationEndpoint: "http://www.yourserver.com/auth",
      });

      // URL generation
      var imageURL = imagekit.url({
        path: "/default-image.jpg",
        transformation: [
          {
            height: "300",
            width: "400",
          },
        ],
      });

      // Upload function internally uses the ImageKit.io javascript SDK
      function upload(data) {
        var file = document.getElementById("file1");
        imagekit.upload(
          {
            file: file.files[0],
            fileName: "abc1.jpg",
            tags: ["tag1"],
          },
          function (err, result) {
            console.log(arguments);
            console.log(
              imagekit.url({
                src: result.url,
                transformation: [{ height: 300, width: 400 }],
              })
            );
          }
        );
      }
    </script>
  </body>
</html>
