// ImageKit Plan Configuration
// Update these values with your actual plan limits from your ImageKit dashboard

module.exports = {
    // Set your actual plan limits here
    // You can find these in your ImageKit dashboard under billing/usage section
    planLimits: {
        // Bandwidth limit in GB per month
        bandwidthGB: 20, // Change this to your actual limit (e.g., 20, 40, 100, 250, 500)
        
        // Storage limit in GB
        storageGB: 20, // Change this to your actual limit (e.g., 20, 40, 100, 250, 500)
        
        // Plan name (optional, for display purposes)
        planName: 'Free Plan' // e.g., 'Free Plan', 'Starter', 'Basic', 'Standard', 'Pro'
    },
    
    // Common ImageKit plan configurations for reference
    // Uncomment and modify the one that matches your plan
    
    // Free Plan
    // planLimits: { bandwidthGB: 20, storageGB: 20, planName: 'Free Plan' },
    
    // Starter Plan  
    // planLimits: { bandwidthGB: 40, storageGB: 40, planName: 'Starter Plan' },
    
    // Basic Plan
    // planLimits: { bandwidthGB: 100, storageGB: 100, planName: 'Basic Plan' },
    
    // Standard Plan
    // planLimits: { bandwidthGB: 250, storageGB: 250, planName: 'Standard Plan' },
    
    // Pro Plan
    // planLimits: { bandwidthGB: 500, storageGB: 500, planName: 'Pro Plan' },
    
    // Custom Plan (if you have a custom plan)
    // planLimits: { bandwidthGB: YOUR_LIMIT, storageGB: YOUR_LIMIT, planName: 'Custom Plan' },
    
    // Alert thresholds (optional)
    alerts: {
        // Send alert when usage exceeds these percentages
        bandwidthWarningPercent: 80, // Alert at 80% bandwidth usage
        storageWarningPercent: 80,   // Alert at 80% storage usage
        bandwidthCriticalPercent: 95, // Critical alert at 95% bandwidth usage
        storageCriticalPercent: 95    // Critical alert at 95% storage usage
    }
};
