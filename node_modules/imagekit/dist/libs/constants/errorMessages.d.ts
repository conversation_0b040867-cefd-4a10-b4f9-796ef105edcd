declare const _default: {
    MA<PERSON><PERSON>ORY_INITIALIZATION_MISSING: {
        message: string;
        help: string;
    };
    <PERSON><PERSON><PERSON><PERSON><PERSON>_PUBLIC_KEY_MISSING: {
        message: string;
        help: string;
    };
    MAND<PERSON>ORY_PRIVATE_KEY_MISSING: {
        message: string;
        help: string;
    };
    MANDATORY_URL_ENDPOINT_KEY_MISSING: {
        message: string;
        help: string;
    };
    INVALID_TRANSFORMATION_POSITION: {
        message: string;
        help: string;
    };
    CACHE_PURGE_URL_MISSING: {
        message: string;
        help: string;
    };
    CACHE_PURGE_STATUS_ID_MISSING: {
        message: string;
        help: string;
    };
    FILE_ID_MISSING: {
        message: string;
        help: string;
    };
    FILE_VERSION_ID_MISSING: {
        message: string;
        help: string;
    };
    FILE_ID_OR_URL_MISSING: {
        message: string;
        help: string;
    };
    INVALID_LIST_OPTIONS: {
        message: string;
        help: string;
    };
    UPDATE_DATA_MISSING: {
        message: string;
        help: string;
    };
    UPDATE_DATA_TAGS_INVALID: {
        message: string;
        help: string;
    };
    UPDATE_DATA_COORDS_INVALID: {
        message: string;
        help: string;
    };
    LIST_FILES_INPUT_MISSING: {
        message: string;
        help: string;
    };
    MISSING_UPLOAD_DATA: {
        message: string;
        help: string;
    };
    MISSING_UPLOAD_FILE_PARAMETER: {
        message: string;
        help: string;
    };
    MISSING_UPLOAD_FILENAME_PARAMETER: {
        message: string;
        help: string;
    };
    JOB_ID_MISSING: {
        message: string;
        help: string;
    };
    INVALID_DESTINATION_FOLDER_PATH: {
        message: string;
        help: string;
    };
    INVALID_INCLUDE_VERSION: {
        message: string;
        help: string;
    };
    INVALID_SOURCE_FILE_PATH: {
        message: string;
        help: string;
    };
    INVALID_SOURCE_FOLDER_PATH: {
        message: string;
        help: string;
    };
    INVALID_FOLDER_NAME: {
        message: string;
        help: string;
    };
    INVALID_PARENT_FOLDER_PATH: {
        message: string;
        help: string;
    };
    INVALID_FOLDER_PATH: {
        message: string;
        help: string;
    };
    INVALID_PHASH_VALUE: {
        message: string;
        help: string;
    };
    MISSING_PHASH_VALUE: {
        message: string;
        help: string;
    };
    UNEQUAL_STRING_LENGTH: {
        message: string;
        help: string;
    };
    INVALID_FILEIDS_VALUE: {
        message: string;
        help: string;
    };
    BULK_ADD_TAGS_INVALID: {
        message: string;
        help: string;
    };
    BULK_AI_TAGS_INVALID: {
        message: string;
        help: string;
    };
    CMF_NAME_MISSING: {
        message: string;
        help: string;
    };
    CMF_LABEL_MISSING: {
        message: string;
        help: string;
    };
    CMF_SCHEMA_MISSING: {
        message: string;
        help: string;
    };
    CMF_SCHEMA_INVALID: {
        message: string;
        help: string;
    };
    CMF_LABEL_SCHEMA_MISSING: {
        message: string;
        help: string;
    };
    CMF_FIELD_ID_MISSING: {
        message: string;
        help: string;
    };
    INVALID_FILE_PATH: {
        message: string;
        help: string;
    };
    INVALID_NEW_FILE_NAME: {
        message: string;
        help: string;
    };
    INVALID_PURGE_CACHE: {
        message: string;
        help: string;
    };
    VERIFY_WEBHOOK_EVENT_SIGNATURE_INCORRECT: {
        message: string;
        help: string;
    };
    VERIFY_WEBHOOK_EVENT_SIGNATURE_MISSING: {
        message: string;
        help: string;
    };
    VERIFY_WEBHOOK_EVENT_TIMESTAMP_MISSING: {
        message: string;
        help: string;
    };
    VERIFY_WEBHOOK_EVENT_TIMESTAMP_INVALID: {
        message: string;
        help: string;
    };
    INVALID_TRANSFORMATION: {
        message: string;
        help: string;
    };
    INVALID_PRE_TRANSFORMATION: {
        message: string;
        help: string;
    };
    INVALID_POST_TRANSFORMATION: {
        message: string;
        help: string;
    };
};
export default _default;
