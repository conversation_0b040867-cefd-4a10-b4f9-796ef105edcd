"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/*
    VARIABLES
*/
var supportedTransforms_1 = __importDefault(require("../libs/constants/supportedTransforms"));
var DEFAULT_TRANSFORMATION_POSITION = "path";
var QUERY_TRANSFORMATION_POSITION = "query";
var CHAIN_TRANSFORM_DELIMITER = ":";
var TRANSFORM_DELIMITER = ",";
var TRANSFORM_KEY_VALUE_DELIMITER = "-";
var getDefault = function () {
    return DEFAULT_TRANSFORMATION_POSITION;
};
var addAsQueryParameter = function (options) {
    return options.transformationPosition === QUERY_TRANSFORMATION_POSITION;
};
var getTransformKey = function (transform) {
    if (!transform) {
        return "";
    }
    return supportedTransforms_1.default[transform] || supportedTransforms_1.default[transform.toLowerCase()] || "";
};
var getChainTransformDelimiter = function () {
    return CHAIN_TRANSFORM_DELIMITER;
};
var getTransformDelimiter = function () {
    return TRANSFORM_DELIMITER;
};
var getTransformKeyValueDelimiter = function () {
    return TRANSFORM_KEY_VALUE_DELIMITER;
};
exports.default = {
    getDefault: getDefault,
    addAsQueryParameter: addAsQueryParameter,
    getTransformKey: getTransformKey,
    getChainTransformDelimiter: getChainTransformDelimiter,
    getTransformDelimiter: getTransformDelimiter,
    getTransformKeyValueDelimiter: getTransformKeyValueDelimiter,
};
