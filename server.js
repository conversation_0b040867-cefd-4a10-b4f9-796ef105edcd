var ImageKit = require("imagekit");
var fs = require('fs');
var https = require('https');

var imagekit = new ImageKit({
    publicKey : "public_Q8avZAesIfF2SEOFiBwIqukGGXk=",
    privateKey : "private_ko9POvLTH2+l1VTJlUr+bFwB6AE=",
    urlEndpoint : "https://ik.imagekit.io/aayang"
});

var authenticationParameters = imagekit.getAuthenticationParameters();
console.log(authenticationParameters);

// Function to get account usage/limits
function getAccountUsage(startDate, endDate) {
    return new Promise((resolve, reject) => {
        // Construct query parameters
        const queryParams = new URLSearchParams();
        if (startDate) queryParams.append('startDate', startDate);
        if (endDate) queryParams.append('endDate', endDate);

        const path = `/v1/accounts/usage${queryParams.toString() ? '?' + queryParams.toString() : ''}`;

        // Create basic auth header
        const auth = Buffer.from(imagekit.options.privateKey + ':').toString('base64');

        const options = {
            hostname: 'api.imagekit.io',
            port: 443,
            path: path,
            method: 'GET',
            headers: {
                'Authorization': `Basic ${auth}`,
                'Content-Type': 'application/json'
            }
        };

        const req = https.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    if (res.statusCode === 200) {
                        resolve(response);
                    } else {
                        reject(new Error(`API Error: ${res.statusCode} - ${response.message || data}`));
                    }
                } catch (error) {
                    reject(new Error(`Failed to parse response: ${error.message}`));
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        req.end();
    });
}

// Example usage - get account usage for different time periods
async function checkAccountLimits() {
    try {
        // Get current date and first day of current month
        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

        const startDate = startOfMonth.toISOString().split('T')[0]; // Format: YYYY-MM-DD
        const endDate = now.toISOString().split('T')[0];

        console.log(`Getting usage from ${startDate} to ${endDate}...`);

        const usage = await getAccountUsage(startDate, endDate);
        console.log('Current Month Usage:', JSON.stringify(usage, null, 2));

        // Get usage for the last 30 days
        const thirtyDaysAgo = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));
        const thirtyDaysAgoStr = thirtyDaysAgo.toISOString().split('T')[0];

        console.log(`\nGetting usage for last 30 days (${thirtyDaysAgoStr} to ${endDate})...`);
        const last30DaysUsage = await getAccountUsage(thirtyDaysAgoStr, endDate);
        console.log('Last 30 Days Usage:', JSON.stringify(last30DaysUsage, null, 2));

        // Display usage in a more readable format
        console.log('\n=== USAGE SUMMARY ===');
        console.log(`Bandwidth Used: ${(usage.bandwidthBytes / (1024 * 1024 * 1024)).toFixed(2)} GB`);
        console.log(`Storage Used: ${(usage.mediaLibraryStorageBytes / (1024 * 1024)).toFixed(2)} MB`);
        console.log(`Video Processing Units: ${usage.videoProcessingUnitsCount}`);
        console.log(`Extension Units: ${usage.extensionUnitsCount}`);
        console.log(`Original Cache Storage: ${(usage.originalCacheStorageBytes / (1024 * 1024)).toFixed(2)} MB`);

    } catch (error) {
        console.error('Error getting account usage:', error.message);
    }
}

// Helper function to get account limits/plan information
async function getAccountLimits() {
    try {
        // For plan limits, you would typically check your ImageKit dashboard
        // or contact ImageKit support for specific limit information
        // This function shows current usage which helps understand consumption

        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const startDate = startOfMonth.toISOString().split('T')[0];
        const endDate = now.toISOString().split('T')[0];

        const usage = await getAccountUsage(startDate, endDate);

        return {
            currentUsage: usage,
            usageFormatted: {
                bandwidthGB: (usage.bandwidthBytes / (1024 * 1024 * 1024)).toFixed(2),
                storageMB: (usage.mediaLibraryStorageBytes / (1024 * 1024)).toFixed(2),
                videoProcessingUnits: usage.videoProcessingUnitsCount,
                extensionUnits: usage.extensionUnitsCount,
                originalCacheStorageMB: (usage.originalCacheStorageBytes / (1024 * 1024)).toFixed(2)
            },
            period: {
                startDate,
                endDate
            }
        };
    } catch (error) {
        throw new Error(`Failed to get account limits: ${error.message}`);
    }
}

// Export the functions for use in other modules
module.exports = {
    imagekit,
    getAccountUsage,
    getAccountLimits,
    checkAccountLimits
};

// Call the function to check account limits (only if running directly)
if (require.main === module) {
    checkAccountLimits();
}