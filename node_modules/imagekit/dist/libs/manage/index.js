"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var cache_1 = __importDefault(require("./cache"));
var file_1 = __importDefault(require("./file"));
var custom_metadata_field_1 = __importDefault(require("./custom-metadata-field"));
exports.default = {
    listFiles: file_1.default.listFiles,
    getFileDetails: file_1.default.getDetails,
    getFileVersions: file_1.default.getFilesVersions,
    getFileVersionDetails: file_1.default.getFileVersionDetails,
    updateFileDetails: file_1.default.updateDetails,
    getFileMetadata: file_1.default.getMetadata,
    deleteFile: file_1.default.deleteFile,
    bulkDeleteFiles: file_1.default.bulkDeleteFiles,
    deleteFileVersion: file_1.default.deleteFileVersion,
    restoreFileVersion: file_1.default.restoreFileVersion,
    bulkAddTags: file_1.default.bulkAddTags,
    bulkRemoveTags: file_1.default.bulkRemoveTags,
    bulkRemoveAITags: file_1.default.bulkRemoveAITags,
    copyFile: file_1.default.copyFile,
    moveFile: file_1.default.moveFile,
    renameFile: file_1.default.renameFile,
    copyFolder: file_1.default.copyFolder,
    moveFolder: file_1.default.moveFolder,
    createFolder: file_1.default.createFolder,
    deleteFolder: file_1.default.deleteFolder,
    getBulkJobStatus: file_1.default.getBulkJobStatus,
    purgeCache: cache_1.default.purgeCache,
    getPurgeCacheStatus: cache_1.default.getPurgeCacheStatus,
    createCustomMetadataField: custom_metadata_field_1.default.create,
    getCustomMetadataFields: custom_metadata_field_1.default.list,
    updateCustomMetadataField: custom_metadata_field_1.default.update,
    deleteCustomMetadataField: custom_metadata_field_1.default.deleteField,
};
